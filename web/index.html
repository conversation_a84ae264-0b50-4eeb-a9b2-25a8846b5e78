<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Rathin - Flutter Developer Portfolio. Experienced Flutter developer specializing in cross-platform mobile, web, and desktop applications with Firebase integration.">
  <meta name="keywords" content="Flutter Developer, Mobile App Development, Firebase, Cross-platform, UI/UX, Dart, Portfolio">
  <meta name="author" content="Rathin">
  <meta property="og:title" content="Rathin - Flutter Developer Portfolio">
  <meta property="og:description" content="Experienced Flutter developer specializing in cross-platform applications">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://your-domain.com">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Rathin - Flutter Developer Portfolio">
  <meta name="twitter:description" content="Experienced Flutter developer specializing in cross-platform applications">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="my_port">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Rathin - Flutter Developer Portfolio</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
