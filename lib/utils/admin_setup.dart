import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AdminSetup {
  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'r@THIN007008';
  
  /// Create admin account if it doesn't exist
  static Future<bool> createAdminAccount() async {
    try {
      print('🔧 Setting up admin account...');
      
      // Check if admin account already exists
      final methods = await FirebaseAuth.instance.fetchSignInMethodsForEmail(adminEmail);
      
      if (methods.isNotEmpty) {
        print('✅ Admin account already exists');
        return true;
      }
      
      // Create admin account
      final credential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: adminEmail,
        password: adminPassword,
      );
      
      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName('Rathin (Admin)');
        
        // Save admin data to Firestore
        await FirebaseFirestore.instance
            .collection('users')
            .doc(credential.user!.uid)
            .set({
          'uid': credential.user!.uid,
          'email': adminEmail,
          'name': '<PERSON>hin (Admin)',
          'role': 'admin',
          'isAdmin': true,
          'createdAt': FieldValue.serverTimestamp(),
          'lastSignIn': FieldValue.serverTimestamp(),
          'provider': 'email',
        });
        
        print('✅ Admin account created successfully');
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ Error creating admin account: $e');
      return false;
    }
  }
  
  /// Test admin login
  static Future<bool> testAdminLogin() async {
    try {
      print('🔐 Testing admin login...');
      
      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: adminEmail,
        password: adminPassword,
      );
      
      if (credential.user != null) {
        print('✅ Admin login successful');
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ Admin login failed: $e');
      return false;
    }
  }
  
  /// Reset admin password
  static Future<bool> resetAdminPassword() async {
    try {
      print('🔄 Resetting admin password...');
      
      await FirebaseAuth.instance.sendPasswordResetEmail(email: adminEmail);
      
      print('✅ Password reset email sent to $adminEmail');
      return true;
    } catch (e) {
      print('❌ Error resetting password: $e');
      return false;
    }
  }
  
  /// Initialize admin setup
  static Future<void> initialize() async {
    try {
      // Wait a bit for Firebase to initialize
      await Future.delayed(const Duration(seconds: 2));
      
      // Try to create admin account
      await createAdminAccount();
      
    } catch (e) {
      print('❌ Error initializing admin setup: $e');
    }
  }
}
