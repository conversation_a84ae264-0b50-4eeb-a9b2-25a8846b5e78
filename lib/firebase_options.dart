// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBFN9eQyCK2WM9kz-Er19TteUGmSYuRgbE',
    appId: '1:531670640472:web:d4972766c9c190c5ffe72d',
    messagingSenderId: '531670640472',
    projectId: 'mine-fca38',
    authDomain: 'mine-fca38.firebaseapp.com',
    storageBucket: 'mine-fca38.firebasestorage.app',
    measurementId: 'G-S1GNYDRRKD',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCG3_vDvddiW3nNPZ2f-3tVrhylIOB1TZs',
    appId: '1:531670640472:android:3f81d8110f30d511ffe72d',
    messagingSenderId: '531670640472',
    projectId: 'mine-fca38',
    storageBucket: 'mine-fca38.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyASQAibBWlHWARGJ0tIhd8fzXLoRDa0Bk0',
    appId: '1:531670640472:ios:6099e0ea197452c8ffe72d',
    messagingSenderId: '531670640472',
    projectId: 'mine-fca38',
    storageBucket: 'mine-fca38.firebasestorage.app',
    iosBundleId: 'com.example.myPort',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyASQAibBWlHWARGJ0tIhd8fzXLoRDa0Bk0',
    appId: '1:531670640472:ios:6099e0ea197452c8ffe72d',
    messagingSenderId: '531670640472',
    projectId: 'mine-fca38',
    storageBucket: 'mine-fca38.firebasestorage.app',
    iosBundleId: 'com.example.myPort',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBFN9eQyCK2WM9kz-Er19TteUGmSYuRgbE',
    appId: '1:531670640472:web:7d9a80efb4a19935ffe72d',
    messagingSenderId: '531670640472',
    projectId: 'mine-fca38',
    authDomain: 'mine-fca38.firebaseapp.com',
    storageBucket: 'mine-fca38.firebasestorage.app',
    measurementId: 'G-FVRW2DXJKQ',
  );
}
