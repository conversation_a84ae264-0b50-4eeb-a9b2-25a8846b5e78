import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/contact_model.dart';

class ContactService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'contacts';

  static Future<bool> submitContactForm(ContactModel contact) async {
    try {
      await _firestore.collection(_collection).add(contact.toJson());
      return true;
    } catch (e) {
      print('Error submitting contact form: $e');
      return false;
    }
  }

  static Future<List<ContactModel>> getContacts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('timestamp', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ContactModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching contacts: $e');
      return [];
    }
  }

  static Stream<List<ContactModel>> getContactsStream() {
    return _firestore
        .collection(_collection)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ContactModel.fromJson(doc.data()))
            .toList());
  }
}
