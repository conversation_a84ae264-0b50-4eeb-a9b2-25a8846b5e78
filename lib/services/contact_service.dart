import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/contact_model.dart';
import '../utils/constants.dart';

// Conditional Firebase import
import 'package:cloud_firestore/cloud_firestore.dart' if (dart.library.html) 'package:cloud_firestore/cloud_firestore.dart';

class ContactService {
  static const String _collection = 'contacts';

  static Future<bool> submitContactForm(ContactModel contact) async {
    try {
      if (kIsWeb) {
        // Use Firebase for web
        final firestore = FirebaseFirestore.instance;
        await firestore.collection(_collection).add(contact.toJson());

        // Also send email notification
        await _sendEmailNotification(contact);
        return true;
      } else {
        // For mobile platforms, send email directly
        final emailSent = await _sendEmailNotification(contact);
        if (emailSent) {
          print('✅ Email sent successfully to ${AppConstants.email}');
        } else {
          print('⚠️ Email client not available, but message saved locally');
        }
        return true;
      }
    } catch (e) {
      print('Error submitting contact form: $e');
      return false;
    }
  }

  static Future<bool> _sendEmailNotification(ContactModel contact) async {
    try {
      // Create email content with JSON data
      final subject = 'Portfolio Contact: ${contact.subject}';
      final jsonData = contact.toJson();
      final formattedJson = _formatJsonForEmail(jsonData);

      final body = '''
Hello Rathin,

You have received a new message through your portfolio contact form:

📧 CONTACT DETAILS:
Name: ${contact.name}
Email: ${contact.email}
Subject: ${contact.subject}

💬 MESSAGE:
${contact.message}

📊 COMPLETE JSON DATA:
${formattedJson}

🔗 QUICK ACTIONS:
- Reply to: ${contact.email}
- Contact Name: ${contact.name}
- Received: ${contact.timestamp}

---
📱 Sent from your Flutter Portfolio App
🕒 Time: ${contact.timestamp}
🌐 Platform: ${kIsWeb ? 'Web' : 'Mobile'}
🆔 Message ID: ${contact.timestamp.millisecondsSinceEpoch}

Reply directly to this email to respond to ${contact.name}.
''';

      // Try multiple email methods
      bool emailSent = false;

      // Method 1: Try mailto (opens default email client)
      final emailUrl = Uri(
        scheme: 'mailto',
        path: AppConstants.email,
        query: _encodeQueryParameters({
          'subject': subject,
          'body': body,
        }),
      );

      if (await canLaunchUrl(emailUrl)) {
        await launchUrl(emailUrl);
        emailSent = true;
        print('✅ Email client opened successfully');
      }

      // Method 2: Try Gmail web interface as fallback
      if (!emailSent) {
        final gmailUrl = 'https://mail.google.com/mail/?view=cm&fs=1&to=${Uri.encodeComponent(AppConstants.email)}&su=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';
        final gmailUri = Uri.parse(gmailUrl);

        if (await canLaunchUrl(gmailUri)) {
          await launchUrl(gmailUri, mode: LaunchMode.externalApplication);
          emailSent = true;
          print('✅ Gmail web interface opened successfully');
        }
      }

      if (!emailSent) {
        print('❌ Could not open email client');
      }

      return emailSent;
    } catch (e) {
      print('Error sending email notification: $e');
      return false;
    }
  }

  static String _formatJsonForEmail(Map<String, dynamic> jsonData) {
    final buffer = StringBuffer();
    buffer.writeln('{');

    jsonData.forEach((key, value) {
      if (value is String) {
        buffer.writeln('  "$key": "${value.replaceAll('"', '\\"')}",');
      } else if (value is DateTime) {
        buffer.writeln('  "$key": "${value.toIso8601String()}",');
      } else {
        buffer.writeln('  "$key": $value,');
      }
    });

    String result = buffer.toString();
    // Remove last comma and close the JSON
    if (result.endsWith(',\n')) {
      result = result.substring(0, result.length - 2) + '\n';
    }
    result += '}';

    return result;
  }

  static String _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  static Future<List<ContactModel>> getContacts() async {
    try {
      if (kIsWeb) {
        final firestore = FirebaseFirestore.instance;
        final querySnapshot = await firestore
            .collection(_collection)
            .orderBy('timestamp', descending: true)
            .get();

        return querySnapshot.docs
            .map((doc) => ContactModel.fromJson(doc.data()))
            .toList();
      } else {
        // For mobile platforms, return empty list
        return [];
      }
    } catch (e) {
      print('Error fetching contacts: $e');
      return [];
    }
  }

  static Stream<List<ContactModel>> getContactsStream() {
    if (kIsWeb) {
      final firestore = FirebaseFirestore.instance;
      return firestore
          .collection(_collection)
          .orderBy('timestamp', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => ContactModel.fromJson(doc.data()))
              .toList());
    } else {
      // For mobile platforms, return empty stream
      return Stream.value([]);
    }
  }
}
