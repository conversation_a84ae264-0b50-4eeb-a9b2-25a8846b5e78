{"version": 3, "file": "fallbackServiceStub.js", "sourceRoot": "", "sources": ["../../src/fallbackServiceStub.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;AAqCH,kDA0KC;AA7MD,mBAAmB;AACnB,4BAA4B;AAE5B,2CAAmC;AAEnC,uDAAwE;AAExE,yDAAsE;AAEtE,2DAAsD;AACtD,mCAAgD;AAyBhD,SAAgB,mBAAmB,CACjC,IAAuC,EACvC,QAAgB,EAChB,WAAmB,EACnB,WAAmB,EACnB,UAAsB,EACtB,cAOoB,EACpB,eAIO,EACP,YAAqB;IAErB,MAAM,KAAK,GAAG,IAAA,iCAAc,GAAE;QAC5B,CAAC,CAAC,MAAM,CAAC,KAAK;QACd,CAAC,CAAE,oBAAsC,CAAC;IAE5C,MAAM,WAAW,GAAwB;QACvC,6MAA6M;QAC7M,KAAK,EAAE,GAAG,EAAE;YACV,OAAO,EAAC,MAAM,EAAE,GAAG,EAAE,GAAE,CAAC,EAAC,CAAC;QAC5B,CAAC;KACF,CAAC;IACF,KAAK,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,OAAW,EACX,OAAkC,EAClC,SAAyB,EACzB,QAAmB,EACnB,EAAE;YACF,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,EAAE,EAAC;YAEf,iHAAiH;YACjH,oCAAoC;YAEpC,IAAI,eAAgC,CAAC;YACrC,IAAI,CAAC;gBACH,eAAe,GAAG,cAAc,CAC9B,GAAG,EACH,QAAQ,EACR,WAAW,EACX,WAAW,EACX,OAAO,EACP,YAAY,CACb,CAAC;YACJ,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,6DAA6D;gBAC7D,sCAAsC;gBACtC,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAChB,CAAC;gBACD,OAAO;oBACL,MAAM,KAAI,CAAC;iBACZ,CAAC;YACJ,CAAC;YAED,MAAM,gBAAgB,GAAG,IAAA,qCAAkB,GAAE;gBAC3C,CAAC,CAAC,IAAI,eAAe,EAAE;gBACvB,CAAC,CAAC,IAAI,kCAAmB,EAAE,CAAC;YAC9B,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAqB,CAAC;YAC5D,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,MAAM,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;YAChC,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;YACxC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;YACD,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,GAAG,CAAC,CAAC;YAErD,UAAU;iBACP,iBAAiB,EAAE;iBACnB,IAAI,CAAC,UAAU,CAAC,EAAE;gBACjB,MAAM,YAAY,GAAgB;oBAChC,OAAO,EAAE;wBACP,GAAG,UAAU;wBACb,GAAG,OAAO;qBACX;oBACD,IAAI,EAAE,eAAe,CAAC,IAIT;oBACb,MAAM,EAAE,eAAe,CAAC,MAAM;oBAC9B,MAAM,EAAE,YAAY;iBACrB,CAAC;gBACF,IACE,eAAe,CAAC,MAAM,KAAK,KAAK;oBAChC,eAAe,CAAC,MAAM,KAAK,QAAQ,EACnC,CAAC;oBACD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC9B,CAAC;gBACD,OAAO,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;YAClC,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,QAAsC,EAAE,EAAE;gBAC/C,IAAI,QAAQ,CAAC,EAAE,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;oBACtC,IAAA,iBAAQ,EACN,QAAQ,CAAC,IAA+B,EACxC,iBAAiB,EACjB,CAAC,GAAY,EAAE,EAAE;wBACf,IACE,GAAG;4BACH,CAAC,CAAC,eAAe;gCACf,CAAC,GAAG,YAAY,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,EACtD,CAAC;4BACD,IAAI,QAAQ,EAAE,CAAC;gCACb,QAAQ,CAAC,GAAG,CAAC,CAAC;4BAChB,CAAC;4BACD,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;wBACvC,CAAC;oBACH,CAAC,CACF,CAAC;oBACF,OAAO;gBACT,CAAC;qBAAM,CAAC;oBACN,OAAO,OAAO,CAAC,GAAG,CAAC;wBACjB,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,QAAQ,CAAC,WAAW,EAAE;qBACvB,CAAC;yBACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAkC,EAAE,EAAE;wBACtD,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;wBAClD,QAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5B,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE;wBACpB,IAAI,CAAC,eAAe,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;4BAClD,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;gCACvB,IAAI,QAAQ,EAAE,CAAC;oCACb,QAAQ,CAAC,GAAG,CAAC,CAAC;gCAChB,CAAC;gCACD,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;4BACvC,CAAC;iCAAM,IAAI,QAAQ,EAAE,CAAC;gCACpB,QAAQ,CAAC,GAAG,CAAC,CAAC;4BAChB,CAAC;iCAAM,CAAC;gCACN,MAAM,GAAG,CAAC;4BACZ,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACP,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAY,EAAE,EAAE;gBACtB,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,QAAQ,EAAE,CAAC;wBACb,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAChB,CAAC;oBACD,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACvC,CAAC;qBAAM,IAAI,QAAQ,EAAE,CAAC;oBACpB,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;YAEL,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO,iBAAiB,CAAC;YAC3B,CAAC;YACD,OAAO;gBACL,MAAM,EAAE,GAAG,EAAE;oBACX,eAAe,GAAG,IAAI,CAAC;oBACvB,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBAC3B,CAAC;aACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC"}