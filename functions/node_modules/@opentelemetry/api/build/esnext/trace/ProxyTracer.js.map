{"version": 3, "file": "ProxyTracer.js", "sourceRoot": "", "sources": ["../../../src/trace/ProxyTracer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAM1C,MAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC;AAErC;;GAEG;AACH,MAAM,OAAO,WAAW;IAItB,YACU,SAA0B,EAClB,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAH/B,cAAS,GAAT,SAAS,CAAiB;QAClB,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAS;QAChB,YAAO,GAAP,OAAO,CAAgB;IACtC,CAAC;IAEJ,SAAS,CAAC,IAAY,EAAE,OAAqB,EAAE,OAAiB;QAC9D,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,eAAe,CACb,KAAa,EACb,QAAyB,EACzB,QAAsB,EACtB,GAAO;QAEP,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG;IACK,UAAU;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QAEF,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,WAAW,CAAC;SACpB;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { NoopTracer } from './NoopTracer';\nimport { Span } from './span';\nimport { SpanOptions } from './SpanOptions';\nimport { Tracer } from './tracer';\nimport { TracerOptions } from './tracer_options';\n\nconst NOOP_TRACER = new NoopTracer();\n\n/**\n * Proxy tracer provided by the proxy tracer provider\n */\nexport class ProxyTracer implements Tracer {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Tracer;\n\n  constructor(\n    private _provider: TracerDelegator,\n    public readonly name: string,\n    public readonly version?: string,\n    public readonly options?: TracerOptions\n  ) {}\n\n  startSpan(name: string, options?: SpanOptions, context?: Context): Span {\n    return this._getTracer().startSpan(name, options, context);\n  }\n\n  startActiveSpan<F extends (span: Span) => unknown>(\n    _name: string,\n    _options: F | SpanOptions,\n    _context?: F | Context,\n    _fn?: F\n  ): ReturnType<F> {\n    const tracer = this._getTracer();\n    return Reflect.apply(tracer.startActiveSpan, tracer, arguments);\n  }\n\n  /**\n   * Try to get a tracer from the proxy tracer provider.\n   * If the proxy tracer provider has no delegate, return a noop tracer.\n   */\n  private _getTracer() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n\n    const tracer = this._provider.getDelegateTracer(\n      this.name,\n      this.version,\n      this.options\n    );\n\n    if (!tracer) {\n      return NOOP_TRACER;\n    }\n\n    this._delegate = tracer;\n    return this._delegate;\n  }\n}\n\nexport interface TracerDelegator {\n  getDelegateTracer(\n    name: string,\n    version?: string,\n    options?: TracerOptions\n  ): Tracer | undefined;\n}\n"]}