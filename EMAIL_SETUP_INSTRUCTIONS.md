# 📧 Email Setup Instructions for Portfolio Contact Form

## 🎯 Overview
This setup ensures that all contact form submissions from your Flutter portfolio are reliably delivered to `<EMAIL>`.

## 🔧 Required Setup Steps

### Step 1: Enable Gmail App Password

1. **Go to Google Account Settings**: https://myaccount.google.com/
2. **Enable 2-Factor Authentication** (if not already enabled)
3. **Generate App Password**:
   - Go to Security → 2-Step Verification → App passwords
   - Select "Mail" and "Other (Custom name)"
   - Name it "Portfolio Contact Form"
   - Copy the 16-character password (e.g., `abcd efgh ijkl mnop`)

### Step 2: Configure Firebase Functions

Run this command to set the Gmail app password:

```bash
firebase functions:config:set gmail.password="your-16-character-app-password"
```

Example:
```bash
firebase functions:config:set gmail.password="abcd efgh ijkl mnop"
```

### Step 3: Deploy Firebase Functions

```bash
firebase deploy --only functions
```

### Step 4: Deploy Firestore Rules

```bash
firebase deploy --only firestore
```

## 🚀 Testing the Email System

### Test Method 1: Use Your Flutter App
1. Run your Flutter app: `flutter run`
2. Navigate to the contact form
3. Fill out and submit the form
4. Check your Gmail inbox for the email

### Test Method 2: Direct HTTP Test
```bash
curl -X POST https://us-central1-mine-fca38.cloudfunctions.net/sendContactEmailHTTP \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Email",
    "message": "This is a test message from the portfolio contact form.",
    "platform": "Test",
    "deviceInfo": {"test": true}
  }'
```

## 📊 Email Features

### What You'll Receive:
- **Professional HTML Email** with your portfolio branding
- **Complete JSON Data** of the submission
- **Device Information** (screen size, platform, etc.)
- **Timestamp and Message ID** for tracking
- **Direct Reply Capability** to the sender's email

### Email Content Includes:
- Contact person's name, email, subject, message
- Complete JSON data for easy processing
- Device and platform information
- Unique message ID for tracking
- Professional formatting with your portfolio branding

## 🔄 Multiple Delivery Methods

The system uses 3 fallback methods to ensure email delivery:

1. **Cloud Functions** (Primary) - Most reliable
2. **Firestore Trigger** (Backup) - Automatic retry
3. **Direct Email Client** (Fallback) - Opens user's email app

## 🛠️ Troubleshooting

### If emails aren't being received:

1. **Check Gmail App Password**:
   ```bash
   firebase functions:config:get
   ```

2. **Check Function Logs**:
   ```bash
   firebase functions:log
   ```

3. **Test Function Directly**:
   ```bash
   firebase functions:shell
   ```

4. **Verify Firestore Rules**:
   - Check Firebase Console → Firestore → Rules

### Common Issues:

- **App Password**: Make sure you're using the 16-character app password, not your regular Gmail password
- **2FA**: Gmail app passwords require 2-factor authentication to be enabled
- **Firestore Rules**: Ensure rules allow write access to the contacts collection

## 📱 Platform Support

- ✅ **Web**: Uses Firebase Cloud Functions SDK
- ✅ **Android**: Uses HTTP endpoint
- ✅ **iOS**: Uses HTTP endpoint  
- ✅ **Desktop**: Uses HTTP endpoint

## 🔐 Security Features

- All emails are sent from your verified Gmail account
- Contact data is stored securely in Firestore
- App passwords are more secure than regular passwords
- CORS protection on HTTP endpoints
- Input validation on all endpoints

## 📈 Analytics & Tracking

Each email includes:
- Unique message ID for tracking
- Timestamp in multiple formats
- Platform and device information
- IP address (when available)
- User agent information

This allows you to track and analyze contact form submissions effectively.
